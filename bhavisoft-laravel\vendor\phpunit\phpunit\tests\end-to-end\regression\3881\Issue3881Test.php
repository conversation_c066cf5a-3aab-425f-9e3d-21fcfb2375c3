<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\TestFixture;

use PHPUnit\Framework\TestCase;

abstract class AbstractIssue3881Test extends TestCase
{
    public function testOne(): void
    {
        $this->assertTrue(true);
    }
}

final class Issue3881Test extends AbstractIssue3881Test
{
}
