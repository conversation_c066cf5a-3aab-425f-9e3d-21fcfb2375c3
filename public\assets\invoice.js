let itemCounter = 1;
let qrDataUrl = '';
// Set default dates
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const dueDate = new Date(today);
    dueDate.setDate(today.getDate() + 30);

    document.getElementById('issueDate').value = today.toISOString().split('T')[0];
    document.getElementById('dueDate').value = dueDate.toISOString().split('T')[0];
    
    updatePreview();
    attachItemEventListeners();
});

// Add item functionality
document.getElementById('addItem').addEventListener('click', function() {
    const itemsContainer = document.getElementById('itemsContainer');
    const newItem = document.createElement('div');
    newItem.className = 'item-row';
    newItem.innerHTML = `
        <div>
            <label>Description</label>
            <input type="text" class="item-description" required>
        </div>
        <div>
            <label>Qty</label>
            <input type="number" class="item-qty" value="1" min="1" required>
        </div>
        <div>
            <label>Rate (₹)</label>
            <input type="number" class="item-rate" step="0.01" required>
        </div>
        <div>
            <label>Amount (₹)</label>
            <input type="number" class="item-amount" readonly>
        </div>
        <div>
            <button type="button" class="btn btn-danger remove-item">×</button>
        </div>
    `;
    itemsContainer.appendChild(newItem);
    attachItemEventListeners();
});

// Attach event listeners to items
function attachItemEventListeners() {
    document.querySelectorAll('.item-qty, .item-rate').forEach(input => {
        input.addEventListener('input', calculateItemAmount);
    });

    document.querySelectorAll('.remove-item').forEach(btn => {
        btn.addEventListener('click', function() {
            if (document.querySelectorAll('.item-row').length > 1) {
                this.closest('.item-row').remove();
                updatePreview();
            }
        });
    });

    document.querySelectorAll('input, textarea, select').forEach(input => {
        input.addEventListener('input', updatePreview);
    });
}

// Calculate item amount
function calculateItemAmount(e) {
    const row = e.target.closest('.item-row');
    const qty = row.querySelector('.item-qty').value;
    const rate = row.querySelector('.item-rate').value;
    const amount = qty * rate;
    row.querySelector('.item-amount').value = amount.toFixed(2);
    updatePreview();
}

// QR code generation for UPI ID
const upiIdInput = document.getElementById('upiId');
if (upiIdInput) {
    upiIdInput.addEventListener('input', function() {
        generateQrFromUpi(upiIdInput.value);
        updatePreview();
    });
}
function generateQrFromUpi(upiId) {
    if (!upiId) {
        qrDataUrl = '';
        document.getElementById('qrPreview').innerHTML = '';
        return;
    }
    // UPI QR payload (simple, for demonstration)
    const upiPayload = `upi://pay?pa=${encodeURIComponent(upiId)}&pn=Payee&cu=INR`;
    const qr = new QRious({
        value: upiPayload,
        size: 100
    });
    qrDataUrl = qr.toDataURL();
    document.getElementById('qrPreview').innerHTML = `<img src='${qrDataUrl}' alt='UPI QR Code'>`;
}

// Update preview
function updatePreview() {
    const preview = document.getElementById('invoicePreview');
    const formData = getFormData();
    
    preview.innerHTML = `
        <div class="invoice-header">
            <div class="company-info">
                <h2>${formData.companyName}</h2>
                <p>${formData.companyAddress.replace(/\n/g, '<br>')}</p>
                <p>📞 ${formData.companyPhone} | ✉️ ${formData.companyEmail}</p>
            </div>
            <div class="invoice-details">
                <div class="invoice-number">#${formData.invoiceNumber}</div>
                <p><strong>Issue Date:</strong> ${formatDate(formData.issueDate)}</p>
                <p><strong>Due Date:</strong> ${formatDate(formData.dueDate)}</p>
                <p><strong>Status:</strong> <span style="color: ${getStatusColor(formData.status)}">${formData.status}</span></p>
            </div>
        </div>

        <div class="billing-section">
            <div class="bill-to">
                <h3>BILL TO</h3>
                <h4>${formData.clientName}</h4>
                <p>${formData.clientAddress.replace(/\n/g, '<br>')}</p>
                <p>📞 ${formData.clientPhone}</p>
            </div>
        </div>

        <table class="items-table">
            <thead>
                <tr>
                    <th>Item & Description</th>
                    <th>Qty</th>
                    <th>Rate</th>
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>
                ${formData.items.map(item => `
                    <tr>
                        <td>${item.description}</td>
                        <td>${item.qty}</td>
                        <td>₹${parseFloat(item.rate).toFixed(2)}</td>
                        <td>₹${parseFloat(item.amount).toFixed(2)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>

        <div class="totals-section">
            <div class="total-row">
                <span>Subtotal</span>
                <span>₹${formData.subtotal.toFixed(2)}</span>
            </div>
            <div class="total-row final">
                <span>Total</span>
                <span>₹${formData.total.toFixed(2)}</span>
            </div>
        </div>

        <div class="payment-section" style="display: flex; align-items: flex-start; gap: 24px;">
            <div style="flex:1;">
                <h3 style="color: #667eea; margin-bottom: 10px;">PAYMENT</h3>
                ${formData.upiPayment ? '<p style="margin-bottom:4px;">✅ UPI Available</p>' : ''}
                <div style="font-size: 13px;">
                    <div><b>Account:</b> ${formData.accountName}</div>
                    <div><b>Number:</b> ${formData.accountNumber}</div>
                    <div><b>IFSC:</b> ${formData.ifscCode}</div>
                    <div><b>Bank:</b> ${formData.bankName}</div>
                </div>
            </div>
            ${qrDataUrl ? `<div><img src='${qrDataUrl}' class='qr-code' alt='UPI QR'></div>` : ''}
        </div>

        <div class="footer">
            <p><strong>Thank you for your business!</strong></p>
            <p>If you have any questions concerning this invoice, please contact us.</p>
            <p>🌐 ${formData.website}</p>
        </div>
    `;
}

// Get form data
function getFormData() {
    const items = [];
    let subtotal = 0;

    document.querySelectorAll('.item-row').forEach(row => {
        const description = row.querySelector('.item-description').value;
        const qty = parseInt(row.querySelector('.item-qty').value) || 0;
        const rate = parseFloat(row.querySelector('.item-rate').value) || 0;
        const amount = qty * rate;

        if (description && qty && rate) {
            items.push({ description, qty, rate, amount });
            subtotal += amount;
        }
    });

    return {
        companyName: document.getElementById('companyName').value,
        companyAddress: document.getElementById('companyAddress').value,
        companyPhone: document.getElementById('companyPhone').value,
        companyEmail: document.getElementById('companyEmail').value,
        invoiceNumber: document.getElementById('invoiceNumber').value,
        issueDate: document.getElementById('issueDate').value,
        dueDate: document.getElementById('dueDate').value,
        status: document.getElementById('status').value,
        clientName: document.getElementById('clientName').value,
        clientAddress: document.getElementById('clientAddress').value,
        clientPhone: document.getElementById('clientPhone').value,
        items: items,
        subtotal: subtotal,
        total: subtotal,
        upiPayment: document.getElementById('upiPayment').checked,
        accountName: document.getElementById('accountName').value,
        accountNumber: document.getElementById('accountNumber').value,
        ifscCode: document.getElementById('ifscCode').value,
        bankName: document.getElementById('bankName').value,
        website: document.getElementById('website').value
    };
}

// Utility functions
function formatDate(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
    });
}

function getStatusColor(status) {
    const colors = {
        'Draft': '#6c757d',
        'Sent': '#007bff',
        'Paid': '#28a745',
        'Overdue': '#dc3545'
    };
    return colors[status] || '#6c757d';
}

// PDF Generation - Direct download approach
document.getElementById('generatePDF').addEventListener('click', function() {
    const button = this;
    button.disabled = true;
    button.textContent = '🔄 Generating PDF...';
    
    try {
        // Get form data
        const formData = getFormData();
        const invoiceHTML = document.getElementById('invoicePreview').innerHTML;
        
        // Create full HTML content for download
        const fullHTML = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Invoice ${formData.invoiceNumber}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
            color: black;
            font-size: 12px;
            line-height: 1.4;
        }
        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        .company-info h2 {
            color: #667eea;
            font-size: 24px;
            margin-bottom: 10px;
        }
        .invoice-details {
            text-align: right;
        }
        .invoice-number {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .billing-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin: 30px 0;
        }
        .bill-to h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 16px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            border: 2px solid #667eea;
        }
        .items-table th {
            background: #667eea;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        .items-table td {
            padding: 15px;
            border-bottom: 1px solid #e1e8ed;
        }
        .items-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        .totals-section {
            margin-left: auto;
            width: 300px;
            margin-top: 20px;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .total-row.final {
            border-bottom: 3px solid #667eea;
            font-weight: bold;
            font-size: 16px;
            color: #667eea;
        }
        .payment-section {
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border: 1px solid #e1e8ed;
        }
        .bank-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #e1e8ed;
            color: #667eea;
            font-weight: 600;
        }
        @media print {
            body { margin: 0; }
        }
    </style>
</head>
<body>
    ${invoiceHTML}
    <div style="text-align: center; margin-top: 30px; page-break-inside: avoid;">
        <p style="color: #667eea; font-size: 14px;">
            📄 To save as PDF: Press Ctrl+P (Windows) or Cmd+P (Mac), then select "Save as PDF"
        </p>
    </div>
</body>
</html>`;
        
        // Create blob and download
        const blob = new Blob([fullHTML], { type: 'text/html;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `Invoice-${formData.invoiceNumber}.html`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        // Show success message
        setTimeout(() => {
            alert('✅ Invoice downloaded as HTML file!\n\n📝 Instructions:\n1. Open the downloaded HTML file in your browser\n2. Press Ctrl+P (Windows) or Cmd+P (Mac)\n3. Select "Save as PDF" as destination\n4. Click Save to get your PDF invoice');
        }, 100);
        
    } catch (error) {
        console.error('Download failed:', error);
        alert('❌ Download failed. Please try again or use a different browser.');
    } finally {
        button.disabled = false;
        button.textContent = '📄 Generate PDF';
    }
});

// Preview button
document.getElementById('previewBtn').addEventListener('click', updatePreview); 