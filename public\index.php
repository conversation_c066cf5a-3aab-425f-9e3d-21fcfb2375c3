<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bhavitech Invoice Generator</title>
    <link rel="stylesheet" href="assets/custom.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrious/4.0.2/qrious.min.js"></script>
</head>
<body>
    <div class="container">
        <div class="form-panel">
            <div class="form-header">
                <h1>Invoice Generator</h1>
                <p style="color: #666;">Create professional invoices with ease</p>
            </div>
            <form id="invoiceForm">
                <!-- Company Information -->
                <div class="form-group">
                    <h3 class="section-title">Company Information</h3>
                    <div class="form-row">
                        <div>
                            <label>Company Name</label>
                            <input type="text" id="companyName" value="Bhavitech" required>
                        </div>
                        <div>
                            <label>Invoice Number</label>
                            <input type="text" id="invoiceNumber" value="INV-959244-B52" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Company Address</label>
                        <textarea id="companyAddress" rows="3">Convent Road, Mittapudur, Fairlands, Salem – 636 016</textarea>
                    </div>
                    <div class="form-row">
                        <div>
                            <label>Phone</label>
                            <input type="text" id="companyPhone" value="7010868809">
                        </div>
                        <div>
                            <label>Email</label>
                            <input type="email" id="companyEmail" value="<EMAIL>">
                        </div>
                    </div>
                </div>
                <!-- Invoice Details -->
                <div class="form-group">
                    <h3 class="section-title">Invoice Details</h3>
                    <div class="form-row">
                        <div>
                            <label>Issue Date</label>
                            <input type="date" id="issueDate" required>
                        </div>
                        <div>
                            <label>Due Date</label>
                            <input type="date" id="dueDate" required>
                        </div>
                        <div>
                            <label>Status</label>
                            <select id="status">
                                <option value="Draft">Draft</option>
                                <option value="Sent">Sent</option>
                                <option value="Paid">Paid</option>
                                <option value="Overdue">Overdue</option>
                            </select>
                        </div>
                    </div>
                </div>
                <!-- Client Information -->
                <div class="form-group">
                    <h3 class="section-title">Bill To</h3>
                    <div class="form-row">
                        <div>
                            <label>Client Name</label>
                            <input type="text" id="clientName" value="KMD Jewellers" required>
                        </div>
                        <div>
                            <label>Client Phone</label>
                            <input type="text" id="clientPhone" value="094434 20303">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Client Address</label>
                        <textarea id="clientAddress" rows="3">No 29, Sharoff Bazzar, Gangapuram, Naganadi, Ambur, Tamil Nadu 635802</textarea>
                    </div>
                </div>
                <!-- Items Section -->
                <div class="items-section">
                    <h3 class="section-title">Invoice Items</h3>
                    <div id="itemsContainer">
                        <div class="item-row">
                            <div>
                                <label>Description</label>
                                <input type="text" class="item-description" value="Jewellers Gold and Silver Rate and Festival, Offer Post Monthly" required>
                            </div>
                            <div>
                                <label>Qty</label>
                                <input type="number" class="item-qty" value="1" min="1" required>
                            </div>
                            <div>
                                <label>Rate (₹)</label>
                                <input type="number" class="item-rate" value="5000" step="0.01" required>
                            </div>
                            <div>
                                <label>Amount (₹)</label>
                                <input type="number" class="item-amount" value="5000" readonly>
                            </div>
                            <div>
                                <button type="button" class="btn btn-danger remove-item">×</button>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-add" id="addItem">+ Add Item</button>
                </div>
                <!-- Payment Information -->
                <div class="form-group payment-compact">
                    <h3 class="section-title">Payment Information</h3>
                    <div class="form-row payment-row">
                        <div>
                            <input type="checkbox" id="upiPayment" checked>
                            <label for="upiPayment" style="margin: 0;">UPI Payment Available</label>
                        </div>
                        <div>
                            <label>Account Name</label>
                            <input type="text" id="accountName" value="Venkatesan Masilamani">
                        </div>
                        <div>
                            <label>Account Number</label>
                            <input type="text" id="accountNumber" value="**************">
                        </div>
                        <div>
                            <label>IFSC Code</label>
                            <input type="text" id="ifscCode" value="FDRL0001527">
                        </div>
                        <div>
                            <label>Bank Name</label>
                            <input type="text" id="bankName" value="FEDERAL BANK, SALEM / ALAGAPURAM">
                        </div>
                        <div>
                            <label>UPI ID</label>
                            <input type="text" id="upiId" placeholder="mvenkatpro@okaxis">
                        </div>
                        <div>
                            <label>UPI QR Code</label>
                            <div id="qrPreview" style="margin-top:8px;"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Website</label>
                        <input type="text" id="website" value="www.bhavitechinc.com">
                    </div>
                </div>
                <div class="action-buttons">
                    <button type="button" class="btn btn-primary" id="generatePDF">📄 Generate PDF</button>
                    <button type="button" class="btn btn-secondary" id="previewBtn">👀 Update Preview</button>
                </div>
            </form>
        </div>
        <div class="preview-panel">
            <div class="invoice-preview" id="invoicePreview">
                <!-- Invoice preview will be generated here -->
            </div>
        </div>
    </div>
    <script src="assets/invoice.js"></script>
</body>
</html> 