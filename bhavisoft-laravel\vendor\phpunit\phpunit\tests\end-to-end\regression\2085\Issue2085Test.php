<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\TestFixture;

use function sleep;
use PHPUnit\Framework\TestCase;

class Issue2085Test extends TestCase
{
    public function testShouldAbortSlowTestByEnforcingTimeLimit(): void
    {
        $this->assertTrue(true);
        sleep(2);
        $this->assertTrue(true);
    }
}
