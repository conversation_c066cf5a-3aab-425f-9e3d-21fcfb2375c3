<?php
$doc = $_SESSION['document'] ?? [];
$client = $_SESSION['client'] ?? [];
$service = $_SESSION['service'] ?? [];
$pricing = $_SESSION['pricing'] ?? [];
$payment = $_SESSION['payment'] ?? [];
$terms = $_SESSION['terms'] ?? '';
$footer = $_SESSION['footer'] ?? [];

function showRow($label, $value) {
    echo "<div class='row mb-1'><div class='col-4 fw-bold'>$label</div><div class='col-8'>$value</div></div>";
}

// Show success/error message
if (isset($_GET['success'])) {
    echo '<div class="alert alert-success">Document saved successfully!</div>';
}
if (isset($_GET['error'])) {
    echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($_GET['error']) . '</div>';
}
?>
<div class="card mb-4">
    <div class="card-header">Document Details</div>
    <div class="card-body">
        <?php showRow('Type', $doc['doc_type'] ?? ''); ?>
        <?php showRow('Number', $doc['doc_number'] ?? ''); ?>
        <?php showRow('Issue Date', $doc['issue_date'] ?? ''); ?>
        <?php showRow('Due Date', $doc['due_date'] ?? ''); ?>
        <?php showRow('Status', $doc['status'] ?? ''); ?>
    </div>
</div>
<div class="card mb-4">
    <div class="card-header">Client / Recipient</div>
    <div class="card-body">
        <?php showRow('Name', $client['name'] ?? ''); ?>
        <?php showRow('Address', nl2br(htmlspecialchars($client['address'] ?? ''))); ?>
        <?php showRow('GSTIN/PAN', $client['gstin_pan'] ?? ''); ?>
        <?php showRow('Contact', $client['contact_number'] ?? ''); ?>
        <?php showRow('Email', $client['email'] ?? ''); ?>
    </div>
</div>
<div class="card mb-4">
    <div class="card-header">Service Details</div>
    <div class="card-body">
        <?php showRow('Main Service', $service['main_service'] ?? ''); ?>
        <?php showRow('Sub-Type', $service['sub_type'] ?? ''); ?>
        <?php showRow('Description', nl2br(htmlspecialchars($service['description'] ?? ''))); ?>
        <?php showRow('Delivery Time', $service['delivery_time'] ?? ''); ?>
    </div>
</div>
<div class="card mb-4">
    <div class="card-header">Pricing & Charges</div>
    <div class="card-body">
        <?php
        $base = $pricing['base_price'] ?? 0;
        $gst = $pricing['gst_percent'] ?? 18;
        $discount = $pricing['discount'] ?? 0;
        $discount_type = $pricing['discount_type'] ?? 'amount';
        $discount_value = $discount_type === 'percent' ? $base * $discount / 100 : $discount;
        $taxable = $base - $discount_value;
        $gst_amt = $taxable * $gst / 100;
        $total = $taxable + $gst_amt;
        showRow('Base Price', '₹' . number_format($base, 2));
        showRow('Discount', '₹' . number_format($discount_value, 2));
        showRow('Taxable Amount', '₹' . number_format($taxable, 2));
        showRow('GST', $gst . '% (₹' . number_format($gst_amt, 2) . ')');
        showRow('Total', '<b>₹' . number_format($total, 2) . '</b>');
        ?>
    </div>
</div>
<div class="card mb-4">
    <div class="card-header">Payment Information</div>
    <div class="card-body">
        <?php showRow('Amount Paid', '₹' . number_format($payment['amount_paid'] ?? 0, 2)); ?>
        <?php showRow('Payment Method', $payment['payment_method'] ?? ''); ?>
        <?php showRow('Reference No.', $payment['payment_ref'] ?? ''); ?>
        <?php showRow('Payment Date', $payment['payment_date'] ?? ''); ?>
        <?php $balance = $total - ($payment['amount_paid'] ?? 0); showRow('Balance Due', '₹' . number_format($balance, 2)); ?>
    </div>
</div>
<div class="card mb-4">
    <div class="card-header">Terms & Conditions</div>
    <div class="card-body">
        <pre class="mb-0" style="white-space: pre-wrap;"><?= htmlspecialchars($terms) ?></pre>
    </div>
</div>
<div class="card mb-4">
    <div class="card-header">Footer</div>
    <div class="card-body">
        <?php showRow('Authorized Signature', $footer['authorized_signature'] ?? ''); ?>
        <?php if (!empty($footer['company_seal'])): ?>
            <div class="mb-2">Company Seal:<br><img src="../assets/<?= htmlspecialchars($footer['company_seal']) ?>" alt="Seal" style="max-height:60px;"></div>
        <?php endif; ?>
        <?php showRow('Footer Text', $footer['footer_text'] ?? ''); ?>
    </div>
</div>
<div class="mb-4">
    <form action="../src/save_to_db.php" method="post" style="display:inline">
        <button type="submit" class="btn btn-success me-2">Save to DB</button>
    </form>
    <a href="../src/pdf_generator.php" class="btn btn-primary me-2" target="_blank">Download PDF</a>
    <a href="#" class="btn btn-secondary me-2">Duplicate</a>
    <a href="#" class="btn btn-warning me-2">Convert</a>
    <a href="#" class="btn btn-info">Add Notes</a>
</div> 