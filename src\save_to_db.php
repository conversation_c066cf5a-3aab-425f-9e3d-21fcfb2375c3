<?php
session_start();
require_once __DIR__ . '/db.php';

$doc = $_SESSION['document'] ?? [];
$client = $_SESSION['client'] ?? [];
$service = $_SESSION['service'] ?? [];
$pricing = $_SESSION['pricing'] ?? [];
$payment = $_SESSION['payment'] ?? [];
$terms = $_SESSION['terms'] ?? '';
$footer = $_SESSION['footer'] ?? [];

try {
    // Insert client (or find existing by name/email)
    $stmt = $pdo->prepare('SELECT id FROM clients WHERE name = ? AND email = ?');
    $stmt->execute([$client['name'], $client['email']]);
    $client_id = $stmt->fetchColumn();
    if (!$client_id) {
        $stmt = $pdo->prepare('INSERT INTO clients (name, address, gstin_pan, contact_number, email) VALUES (?, ?, ?, ?, ?)');
        $stmt->execute([
            $client['name'], $client['address'], $client['gstin_pan'], $client['contact_number'], $client['email']
        ]);
        $client_id = $pdo->lastInsertId();
    }

    // Insert service (or find existing by main_service/sub_type)
    $stmt = $pdo->prepare('SELECT id FROM services WHERE main_service = ? AND sub_type = ?');
    $stmt->execute([$service['main_service'], $service['sub_type']]);
    $service_id = $stmt->fetchColumn();
    if (!$service_id) {
        $stmt = $pdo->prepare('INSERT INTO services (main_service, sub_type, auto_description, delivery_time) VALUES (?, ?, ?, ?)');
        $stmt->execute([
            $service['main_service'], $service['sub_type'], $service['description'], $service['delivery_time']
        ]);
        $service_id = $pdo->lastInsertId();
    }

    // Insert document
    $stmt = $pdo->prepare('INSERT INTO documents (doc_type, doc_number, issue_date, due_date, status, client_id, service_id, base_price, gst_percent, discount, total, terms, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');
    $base = $pricing['base_price'] ?? 0;
    $gst = $pricing['gst_percent'] ?? 18;
    $discount = $pricing['discount'] ?? 0;
    $discount_type = $pricing['discount_type'] ?? 'amount';
    $discount_value = $discount_type === 'percent' ? $base * $discount / 100 : $discount;
    $taxable = $base - $discount_value;
    $gst_amt = $taxable * $gst / 100;
    $total = $taxable + $gst_amt;
    $stmt->execute([
        $doc['doc_type'], $doc['doc_number'], $doc['issue_date'], $doc['due_date'], $doc['status'],
        $client_id, $service_id, $base, $gst, $discount_value, $total, $terms, null
    ]);
    $document_id = $pdo->lastInsertId();

    // Insert payment
    if (!empty($payment['amount_paid'])) {
        $stmt = $pdo->prepare('INSERT INTO payments (document_id, amount_paid, payment_method, payment_ref, payment_date) VALUES (?, ?, ?, ?, ?)');
        $stmt->execute([
            $document_id, $payment['amount_paid'], $payment['payment_method'], $payment['payment_ref'], $payment['payment_date']
        ]);
    }

    // Clear session data
    $_SESSION = [];
    
    header('Location: ../public/index.php?success=1');
    exit;
} catch (Exception $e) {
    header('Location: ../public/index.php?error=' . urlencode($e->getMessage()));
    exit;
} 