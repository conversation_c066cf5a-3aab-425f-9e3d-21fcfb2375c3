# Bhavisoft Smart Document Generator

## Setup Instructions

### 1. Database Setup

- Create a MySQL database (default: `bhavisoft`).
- Import the following table schemas (see below).
- Insert your company info into the `company_info` table.

### 2. Configure Database Connection

- Edit `src/db.php` if your MySQL username/password/database is different.

### 3. Running the App

- Place the project in your web server's root (e.g., `htdocs` for XAMPP).
- Access via `http://localhost/bhavisoft/public/`

---

## MySQL Table Schemas

```
-- company_info
CREATE TABLE company_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(128) NOT NULL,
    logo VARCHAR(255),
    address VARCHAR(255),
    contact_number VARCHAR(32),
    email VARCHAR(128),
    gstin VARCHAR(32)
);

-- clients
CREATE TABLE clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(128) NOT NULL,
    address TEXT,
    gstin_pan VARCHAR(32),
    contact_number VARCHAR(32),
    email VARCHAR(128)
);

-- services
CREATE TABLE services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    main_service VARCHAR(64) NOT NULL,
    sub_type VARCHAR(64),
    auto_description TEXT,
    delivery_time VARCHAR(64)
);

-- documents
CREATE TABLE documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    doc_type ENUM('Invoice','Quotation','Contract') NOT NULL,
    doc_number VARCHAR(32) UNIQUE NOT NULL,
    issue_date DATE NOT NULL,
    due_date DATE,
    status ENUM('Draft','Pending','Approved','Paid','Cancelled') DEFAULT 'Draft',
    client_id INT NOT NULL,
    service_id INT NOT NULL,
    base_price DECIMAL(10,2) NOT NULL,
    gst_percent DECIMAL(5,2) DEFAULT 18.00,
    discount DECIMAL(10,2) DEFAULT 0.00,
    total DECIMAL(10,2) NOT NULL,
    terms TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (service_id) REFERENCES services(id)
);

-- payments
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    amount_paid DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(32),
    payment_ref VARCHAR(64),
    payment_date DATE,
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- templates (optional)
CREATE TABLE templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(128) NOT NULL,
    doc_type ENUM('Invoice','Quotation','Contract') NOT NULL,
    template_data JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## Next Steps
- Add your company info in the database.
- Start building out the document creation forms and logic. 