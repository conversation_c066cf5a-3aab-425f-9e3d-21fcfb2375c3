<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\TestFixture;

use Exception;

final class MyException extends Exception
{
}

throw new MyException('Big boom. Big bada boom.', 0, new Exception('Previous boom.'));
