--TEST--
TestDox: Test triggers notice and --display-notices is not used
--FILE--
<?php declare(strict_types=1);
$_SERVER['argv'][] = '--do-not-cache-result';
$_SERVER['argv'][] = '--no-configuration';
$_SERVER['argv'][] = '--no-progress';
$_SERVER['argv'][] = '--testdox';
$_SERVER['argv'][] = '--colors=never';
$_SERVER['argv'][] = __DIR__ . '/_files/NoticeTest.php';

require_once __DIR__ . '/../../bootstrap.php';

(new PHPUnit\TextUI\Application)->run($_SERVER['argv']);
--EXPECTF--
PHPUnit %s by <PERSON> and contributors.

Runtime: %s

Time: %s, Memory: %s

Notice (PHPUnit\TestFixture\TestDox\Notice)
 ⚠ Notice

OK, but there were issues!
Tests: 1, Assertions: 1, Notices: 1.
