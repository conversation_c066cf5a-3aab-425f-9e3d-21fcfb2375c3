<?php
session_start();
require_once __DIR__ . '/fpdf.php';

$doc = $_SESSION['document'] ?? [];
$client = $_SESSION['client'] ?? [];
$service = $_SESSION['service'] ?? [];
$pricing = $_SESSION['pricing'] ?? [];
$payment = $_SESSION['payment'] ?? [];
$terms = $_SESSION['terms'] ?? '';
$footer = $_SESSION['footer'] ?? [];

$pdf = new FPDF();
$pdf->AddPage();
$pdf->SetFont('Arial', 'B', 16);
$pdf->Cell(0, 10, $doc['doc_type'] . ' - ' . ($doc['doc_number'] ?? ''), 0, 1, 'C');
$pdf->SetFont('Arial', '', 12);
$pdf->Cell(0, 8, 'Issue Date: ' . ($doc['issue_date'] ?? ''), 0, 1);
$pdf->Cell(0, 8, 'Due Date: ' . ($doc['due_date'] ?? ''), 0, 1);
$pdf->Cell(0, 8, 'Status: ' . ($doc['status'] ?? ''), 0, 1);
$pdf->Ln(4);
$pdf->SetFont('Arial', 'B', 14);
$pdf->Cell(0, 8, 'Client / Recipient', 0, 1);
$pdf->SetFont('Arial', '', 12);
$pdf->Cell(0, 8, 'Name: ' . ($client['name'] ?? ''), 0, 1);
$pdf->MultiCell(0, 8, 'Address: ' . ($client['address'] ?? ''));
$pdf->Cell(0, 8, 'GSTIN/PAN: ' . ($client['gstin_pan'] ?? ''), 0, 1);
$pdf->Cell(0, 8, 'Contact: ' . ($client['contact_number'] ?? ''), 0, 1);
$pdf->Cell(0, 8, 'Email: ' . ($client['email'] ?? ''), 0, 1);
$pdf->Ln(4);
$pdf->SetFont('Arial', 'B', 14);
$pdf->Cell(0, 8, 'Service Details', 0, 1);
$pdf->SetFont('Arial', '', 12);
$pdf->Cell(0, 8, 'Main Service: ' . ($service['main_service'] ?? ''), 0, 1);
$pdf->Cell(0, 8, 'Sub-Type: ' . ($service['sub_type'] ?? ''), 0, 1);
$pdf->MultiCell(0, 8, 'Description: ' . ($service['description'] ?? ''));
$pdf->Cell(0, 8, 'Delivery Time: ' . ($service['delivery_time'] ?? ''), 0, 1);
$pdf->Ln(4);
$pdf->SetFont('Arial', 'B', 14);
$pdf->Cell(0, 8, 'Pricing & Charges', 0, 1);
$pdf->SetFont('Arial', '', 12);
$base = $pricing['base_price'] ?? 0;
$gst = $pricing['gst_percent'] ?? 18;
$discount = $pricing['discount'] ?? 0;
$discount_type = $pricing['discount_type'] ?? 'amount';
$discount_value = $discount_type === 'percent' ? $base * $discount / 100 : $discount;
$taxable = $base - $discount_value;
$gst_amt = $taxable * $gst / 100;
$total = $taxable + $gst_amt;
$pdf->Cell(0, 8, 'Base Price: ₹' . number_format($base, 2), 0, 1);
$pdf->Cell(0, 8, 'Discount: ₹' . number_format($discount_value, 2), 0, 1);
$pdf->Cell(0, 8, 'Taxable Amount: ₹' . number_format($taxable, 2), 0, 1);
$pdf->Cell(0, 8, 'GST (' . $gst . '%): ₹' . number_format($gst_amt, 2), 0, 1);
$pdf->Cell(0, 8, 'Total: ₹' . number_format($total, 2), 0, 1);
$pdf->Ln(4);
$pdf->SetFont('Arial', 'B', 14);
$pdf->Cell(0, 8, 'Payment Information', 0, 1);
$pdf->SetFont('Arial', '', 12);
$pdf->Cell(0, 8, 'Amount Paid: ₹' . number_format($payment['amount_paid'] ?? 0, 2), 0, 1);
$pdf->Cell(0, 8, 'Payment Method: ' . ($payment['payment_method'] ?? ''), 0, 1);
$pdf->Cell(0, 8, 'Reference No.: ' . ($payment['payment_ref'] ?? ''), 0, 1);
$pdf->Cell(0, 8, 'Payment Date: ' . ($payment['payment_date'] ?? ''), 0, 1);
$pdf->Cell(0, 8, 'Balance Due: ₹' . number_format($total - ($payment['amount_paid'] ?? 0), 2), 0, 1);
$pdf->Ln(4);
$pdf->SetFont('Arial', 'B', 14);
$pdf->Cell(0, 8, 'Terms & Conditions', 0, 1);
$pdf->SetFont('Arial', '', 12);
$pdf->MultiCell(0, 8, $terms);
$pdf->Ln(4);
$pdf->SetFont('Arial', 'B', 14);
$pdf->Cell(0, 8, 'Footer', 0, 1);
$pdf->SetFont('Arial', '', 12);
$pdf->Cell(0, 8, 'Authorized Signature: ' . ($footer['authorized_signature'] ?? ''), 0, 1);
$pdf->Cell(0, 8, 'Footer Text: ' . ($footer['footer_text'] ?? ''), 0, 1);
$pdf->Output('D', ($doc['doc_type'] ?? 'Document') . '-' . ($doc['doc_number'] ?? '') . '.pdf');
exit; 