<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\TestFixture\Issue5493;

use PHPUnit\Framework\TestCase;

final class Issue5493Test extends TestCase
{
    protected function setUp(): void
    {
        /** @noinspection PhpUnitAssertCanBeReplacedWithFailInspection */
        $this->assertTrue(false);
    }

    public function testOne(): void
    {
        $this->assertTrue(true);
    }
}
