<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\TestFixture\Issue5844;

use function set_error_handler;

final class CustomErrorHandler
{
    public function __construct()
    {
        set_error_handler([$this, 'handleError']);
    }

    private function handleError(): void
    {
    }
}

new CustomErrorHandler;
