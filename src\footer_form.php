<?php
// Handle file upload for seal
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['company_seal']) && $_FILES['company_seal']['error'] === UPLOAD_ERR_OK) {
    $upload_dir = __DIR__ . '/../assets/';
    $filename = 'seal_' . time() . '_' . basename($_FILES['company_seal']['name']);
    $target = $upload_dir . $filename;
    if (move_uploaded_file($_FILES['company_seal']['tmp_name'], $target)) {
        $_SESSION['footer']['company_seal'] = $filename;
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['authorized_signature'])) {
    $_SESSION['footer']['authorized_signature'] = $_POST['authorized_signature'];
    $_SESSION['footer']['footer_text'] = $_POST['footer_text'];
}

$authorized_signature = $_SESSION['footer']['authorized_signature'] ?? '';
$company_seal = $_SESSION['footer']['company_seal'] ?? '';
$footer_text = $_SESSION['footer']['footer_text'] ?? 'Thanks for choosing Bhavitech';
?>
<form method="post" enctype="multipart/form-data" class="row g-3 mb-4">
    <h5>Footer</h5>
    <div class="col-md-4">
        <label class="form-label">Authorized Signature</label>
        <input type="text" name="authorized_signature" class="form-control" value="<?= htmlspecialchars($authorized_signature) ?>">
    </div>
    <div class="col-md-4">
        <label class="form-label">Company Seal (optional)</label>
        <input type="file" name="company_seal" class="form-control">
        <?php if ($company_seal): ?>
            <div class="mt-2"><img src="../assets/<?= htmlspecialchars($company_seal) ?>" alt="Seal" style="max-height:60px;"></div>
        <?php endif; ?>
    </div>
    <div class="col-md-4">
        <label class="form-label">Footer Text</label>
        <input type="text" name="footer_text" class="form-control" value="<?= htmlspecialchars($footer_text) ?>">
    </div>
    <div class="col-12">
        <button type="submit" class="btn btn-primary">Save & Continue</button>
    </div>
</form> 