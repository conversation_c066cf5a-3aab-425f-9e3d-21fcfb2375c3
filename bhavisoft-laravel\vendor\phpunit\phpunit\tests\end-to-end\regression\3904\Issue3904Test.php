<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\TestFixture;

use PHPUnit\Framework\TestCase;

class Bar extends TestCase
{
}

final class Issue3904Test extends Bar
{
    public function testOne(): void
    {
        $this->assertTrue(true);
    }
}
