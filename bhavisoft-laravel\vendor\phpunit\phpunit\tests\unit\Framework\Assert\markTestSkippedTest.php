<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\Framework;

use PHPUnit\Framework\Attributes\CoversMethod;
use PHPUnit\Framework\Attributes\Small;
use PHPUnit\Framework\Attributes\TestDox;

#[CoversMethod(Assert::class, 'markTestSkipped')]
#[TestDox('markTestSkipped()')]
#[Small]
final class markTestSkippedTest extends TestCase
{
    public function testMarksTestAsSkipped(): void
    {
        $message = 'message';

        $this->expectException(SkippedWithMessageException::class);
        $this->expectExceptionMessage($message);

        $this->markTestSkipped($message);
    }
}
