<?php
// session_start();

// Service data mapping
$services = [
    'Website' => [
        'Static' => [
            'desc' => '5-page responsive static site',
            'delivery' => '7 working days',
        ],
        'Dynamic' => [
            'desc' => 'CMS-based site with admin panel',
            'delivery' => '12 working days',
        ],
        'E-Commerce' => [
            'desc' => 'Online store with cart, payment gateway',
            'delivery' => '15 working days',
        ],
    ],
    'Software' => [
        'Billing App' => [
            'desc' => 'GST-ready billing system with reports',
            'delivery' => '20 working days',
        ],
    ],
    'Digital Marketing' => [
        'SEO' => [
            'desc' => 'Monthly on-page + backlinking',
            'delivery' => '30 days',
        ],
    ],
    'Hosting' => [
        'Basic' => [
            'desc' => '1 year shared hosting + domain',
            'delivery' => '2 working days',
        ],
    ],
];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['main_service'])) {
    $_SESSION['service'] = [
        'main_service' => $_POST['main_service'],
        'sub_type' => $_POST['sub_type'],
        'description' => $_POST['description'],
        'delivery_time' => $_POST['delivery_time'],
    ];
}

$main_service = $_SESSION['service']['main_service'] ?? '';
$sub_type = $_SESSION['service']['sub_type'] ?? '';
$description = $_SESSION['service']['description'] ?? '';
$delivery_time = $_SESSION['service']['delivery_time'] ?? '';

// For dynamic sub-type and auto-fill
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['main_service'])) {
    $main_service = $_POST['main_service'];
    $sub_type = $_POST['sub_type'] ?? '';
    if ($main_service && $sub_type && isset($services[$main_service][$sub_type])) {
        $description = $services[$main_service][$sub_type]['desc'];
        $delivery_time = $services[$main_service][$sub_type]['delivery'];
    }
}

?>
<form method="post" class="row g-3 mb-4">
    <h5>Service Details & Auto Content</h5>
    <div class="col-md-4">
        <label class="form-label">Main Service</label>
        <select name="main_service" class="form-select" onchange="this.form.submit()">
            <option value="">Select Service</option>
            <?php foreach ($services as $main => $subs): ?>
                <option value="<?= $main ?>" <?= $main_service == $main ? 'selected' : '' ?>><?= $main ?></option>
            <?php endforeach; ?>
        </select>
    </div>
    <div class="col-md-4">
        <label class="form-label">Sub-Type</label>
        <select name="sub_type" class="form-select" onchange="this.form.submit()">
            <option value="">Select Sub-Type</option>
            <?php if ($main_service && isset($services[$main_service])): ?>
                <?php foreach ($services[$main_service] as $sub => $info): ?>
                    <option value="<?= $sub ?>" <?= $sub_type == $sub ? 'selected' : '' ?>><?= $sub ?></option>
                <?php endforeach; ?>
            <?php endif; ?>
        </select>
    </div>
    <div class="col-md-4">
        <label class="form-label">Delivery Time</label>
        <input type="text" name="delivery_time" class="form-control" value="<?= htmlspecialchars($delivery_time) ?>" readonly>
    </div>
    <div class="col-12">
        <label class="form-label">Auto-Generated Description</label>
        <textarea name="description" class="form-control" rows="2" readonly><?= htmlspecialchars($description) ?></textarea>
    </div>
    <div class="col-12">
        <button type="submit" class="btn btn-primary">Save & Continue</button>
    </div>
</form> 