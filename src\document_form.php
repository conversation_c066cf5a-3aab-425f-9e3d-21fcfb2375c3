<?php

date_default_timezone_set('Asia/Kolkata');

// Document types and statuses
$doc_types = [
    'Invoice' => 'Invoice',
    'Quotation' => 'Quotation',
    'Contract' => 'Contract',
];
$statuses = [
    'Draft', 'Pending', 'Approved', 'Paid', 'Cancelled'
];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['doc_type'])) {
    $_SESSION['document'] = [
        'doc_type' => $_POST['doc_type'],
        'doc_number' => $_POST['doc_number'],
        'issue_date' => $_POST['issue_date'],
        'due_date' => $_POST['due_date'],
        'status' => $_POST['status'],
    ];
}

// Generate auto-number
function generateDocNumber($type) {
    $prefix = strtoupper(substr($type, 0, 3));
    $date = date('Ym');
    // In real app, fetch last number from DB. Here, use 0001 for demo.
    $seq = '0001';
    return "$prefix-$date-$seq";
}

$selected_type = $_SESSION['document']['doc_type'] ?? 'Invoice';
$auto_number = generateDocNumber($selected_type);
$issue_date = $_SESSION['document']['issue_date'] ?? date('Y-m-d');
$due_date = $_SESSION['document']['due_date'] ?? '';
$status = $_SESSION['document']['status'] ?? 'Draft';
?>
<form method="post" class="row g-3 mb-4">
    <div class="col-md-3">
        <label class="form-label">Document Type</label>
        <select name="doc_type" class="form-select" onchange="this.form.submit()">
            <?php foreach ($doc_types as $key => $label): ?>
                <option value="<?= $key ?>" <?= $selected_type == $key ? 'selected' : '' ?>><?= $label ?></option>
            <?php endforeach; ?>
        </select>
    </div>
    <div class="col-md-3">
        <label class="form-label">Auto Number</label>
        <input type="text" name="doc_number" class="form-control" value="<?= $auto_number ?>" readonly>
    </div>
    <div class="col-md-2">
        <label class="form-label">Issue Date</label>
        <input type="date" name="issue_date" class="form-control" value="<?= $issue_date ?>">
    </div>
    <div class="col-md-2">
        <label class="form-label">Due Date</label>
        <input type="date" name="due_date" class="form-control" value="<?= $due_date ?>">
    </div>
    <div class="col-md-2">
        <label class="form-label">Status</label>
        <select name="status" class="form-select">
            <?php foreach ($statuses as $s): ?>
                <option value="<?= $s ?>" <?= $status == $s ? 'selected' : '' ?>><?= $s ?></option>
            <?php endforeach; ?>
        </select>
    </div>
    <div class="col-12">
        <button type="submit" class="btn btn-primary">Save & Continue</button>
    </div>
</form> 