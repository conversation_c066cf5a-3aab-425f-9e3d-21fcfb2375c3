<?php
// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['base_price'])) {
    $_SESSION['pricing'] = [
        'base_price' => (float)$_POST['base_price'],
        'gst_percent' => (float)$_POST['gst_percent'],
        'discount' => (float)$_POST['discount'],
        'discount_type' => $_POST['discount_type'],
    ];
}

$base_price = $_SESSION['pricing']['base_price'] ?? 0.0;
$gst_percent = $_SESSION['pricing']['gst_percent'] ?? 18.0;
$discount = $_SESSION['pricing']['discount'] ?? 0.0;
$discount_type = $_SESSION['pricing']['discount_type'] ?? 'amount';

// Calculate discount value
if ($discount_type === 'percent') {
    $discount_value = $base_price * $discount / 100.0;
} else {
    $discount_value = $discount;
}
$taxable_amount = $base_price - $discount_value;
$gst_amount = $taxable_amount * $gst_percent / 100.0;
$total = $taxable_amount + $gst_amount;

?>
<form method="post" class="row g-3 mb-4">
    <h5>Pricing & Charges Breakdown</h5>
    <div class="col-md-3">
        <label class="form-label">Base Price (₹)</label>
        <input type="number" name="base_price" class="form-control" min="0" step="0.01" value="<?= htmlspecialchars($base_price) ?>" required>
    </div>
    <div class="col-md-2">
        <label class="form-label">GST (%)</label>
        <input type="number" name="gst_percent" class="form-control" min="0" step="0.01" value="<?= htmlspecialchars($gst_percent) ?>">
    </div>
    <div class="col-md-3">
        <label class="form-label">Discount</label>
        <div class="input-group">
            <input type="number" name="discount" class="form-control" min="0" step="0.01" value="<?= htmlspecialchars($discount) ?>">
            <select name="discount_type" class="form-select">
                <option value="amount" <?= $discount_type === 'amount' ? 'selected' : '' ?>>₹</option>
                <option value="percent" <?= $discount_type === 'percent' ? 'selected' : '' ?>>%</option>
            </select>
        </div>
    </div>
    <div class="col-md-4">
        <label class="form-label">Calculation</label>
        <div class="border rounded p-2 bg-light">
            <div>Base: ₹<?= number_format($base_price, 2) ?></div>
            <div>Discount: ₹<?= number_format($discount_value, 2) ?></div>
            <div>Taxable Amount: ₹<?= number_format($taxable_amount, 2) ?></div>
            <div>GST @<?= number_format($gst_percent, 2) ?>%: ₹<?= number_format($gst_amount, 2) ?></div>
            <div class="fw-bold">Total: ₹<?= number_format($total, 2) ?></div>
        </div>
    </div>
    <div class="col-12">
        <button type="submit" class="btn btn-primary">Save & Continue</button>
    </div>
</form> 