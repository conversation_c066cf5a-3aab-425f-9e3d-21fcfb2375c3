<?php
// session_start();

// Get total from pricing
$pricing = $_SESSION['pricing'] ?? [];
$total = isset($pricing['base_price']) ?
    (($pricing['base_price'] - (($pricing['discount_type'] ?? 'amount') === 'percent' ? $pricing['base_price'] * ($pricing['discount'] ?? 0) / 100 : ($pricing['discount'] ?? 0))) * (1 + (($pricing['gst_percent'] ?? 18) / 100))) : 0;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['amount_paid'])) {
    $_SESSION['payment'] = [
        'amount_paid' => (float)$_POST['amount_paid'],
        'payment_method' => $_POST['payment_method'],
        'payment_ref' => $_POST['payment_ref'],
        'payment_date' => $_POST['payment_date'],
    ];
}

$amount_paid = $_SESSION['payment']['amount_paid'] ?? 0.0;
$balance_due = $total - $amount_paid;
$payment_method = $_SESSION['payment']['payment_method'] ?? '';
$payment_ref = $_SESSION['payment']['payment_ref'] ?? '';
$payment_date = $_SESSION['payment']['payment_date'] ?? date('Y-m-d');

?>
<form method="post" class="row g-3 mb-4">
    <h5>Payment Information</h5>
    <div class="col-md-3">
        <label class="form-label">Amount Paid (₹)</label>
        <input type="number" name="amount_paid" class="form-control" min="0" step="0.01" value="<?= htmlspecialchars($amount_paid) ?>">
    </div>
    <div class="col-md-3">
        <label class="form-label">Balance Due (₹)</label>
        <input type="text" class="form-control" value="<?= number_format($balance_due, 2) ?>" readonly>
    </div>
    <div class="col-md-3">
        <label class="form-label">Payment Method</label>
        <select name="payment_method" class="form-select">
            <option value="">Select</option>
            <option value="UPI" <?= $payment_method === 'UPI' ? 'selected' : '' ?>>UPI</option>
            <option value="Bank Transfer" <?= $payment_method === 'Bank Transfer' ? 'selected' : '' ?>>Bank Transfer</option>
            <option value="Cash" <?= $payment_method === 'Cash' ? 'selected' : '' ?>>Cash</option>
        </select>
    </div>
    <div class="col-md-3">
        <label class="form-label">Reference No.</label>
        <input type="text" name="payment_ref" class="form-control" value="<?= htmlspecialchars($payment_ref) ?>">
    </div>
    <div class="col-md-3">
        <label class="form-label">Payment Date</label>
        <input type="date" name="payment_date" class="form-control" value="<?= htmlspecialchars($payment_date) ?>">
    </div>
    <div class="col-12">
        <button type="submit" class="btn btn-primary">Save & Continue</button>
    </div>
</form> 