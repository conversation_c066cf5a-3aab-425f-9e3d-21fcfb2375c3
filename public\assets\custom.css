/* Modern Invoice Generator CSS from user reference */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}
.container {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    align-items: start;
}
.form-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}
.preview-panel {
    background: #fff;
    border-radius: 18px;
    box-shadow: 0 2px 12px rgba(80,80,160,0.12);
    padding: 0 0 24px 0;
}
.form-header {
    text-align: center;
    margin-bottom: 30px;
}
.form-header h1 {
    color: #333;
    font-size: 2.2em;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
.form-group {
    margin-bottom: 20px;
}
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}
label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
input, textarea, select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}
input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}
.items-section {
    border: 2px dashed #e1e8ed;
    border-radius: 15px;
    padding: 20px;
    margin: 25px 0;
    background: rgba(102, 126, 234, 0.05);
}
.item-row {
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 1fr auto;
    gap: 10px;
    align-items: end;
    margin-bottom: 15px;
    padding: 15px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}
.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}
.btn-secondary {
    background: #6c757d;
    color: white;
}
.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}
.btn-danger {
    background: #dc3545;
    color: white;
    padding: 8px 12px;
    font-size: 12px;
}
.btn-danger:hover {
    background: #c82333;
}
.btn-add {
    background: #28a745;
    color: white;
    width: 100%;
    margin-top: 10px;
}
.btn-add:hover {
    background: #218838;
    transform: translateY(-2px);
}
.invoice-preview {
    padding: 40px;
    background: white;
    font-size: 12px;
    line-height: 1.4;
    min-height: 800px;
}
.invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 3px solid #667eea;
}
.company-info h2 {
    color: #667eea;
    font-size: 24px;
    margin-bottom: 10px;
}
.invoice-details {
    text-align: right;
}
.invoice-number {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
}
.billing-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin: 30px 0;
}
.bill-to h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 16px;
}
.items-table {
    width: 100%;
    border-collapse: collapse;
    margin: 30px 0;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
}
.items-table th {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 15px;
    text-align: left;
    font-weight: 600;
}
.items-table td {
    padding: 15px;
    border-bottom: 1px solid #e1e8ed;
}
.items-table tr:nth-child(even) {
    background: #f8f9fa;
}
.totals-section {
    margin-left: auto;
    width: 300px;
    margin-top: 20px;
}
.total-row {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}
.total-row.final {
    border-bottom: 3px solid #667eea;
    font-weight: bold;
    font-size: 16px;
    color: #667eea;
}
.payment-section {
    margin-top: 40px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}
.payment-method {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}
.payment-method input[type="checkbox"] {
    width: auto;
    margin-right: 10px;
}
.bank-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 15px;
}
.footer {
    text-align: center;
    margin-top: 40px;
    padding-top: 20px;
    border-top: 2px solid #e1e8ed;
    color: #667eea;
    font-weight: 600;
}
.section-title {
    margin-bottom: 15px;
    color: #667eea;
    font-size: 1.2em;
    font-weight: 700;
}
.payment-compact .payment-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 10px;
    align-items: end;
    margin-bottom: 10px;
}
#qrPreview img {
    max-width: 80px;
    max-height: 80px;
    border-radius: 8px;
    border: 1px solid #e1e8ed;
    margin-top: 4px;
}
#qrPreview canvas {
    max-width: 100px;
    max-height: 100px;
    border-radius: 8px;
    border: 1px solid #e1e8ed;
    margin-top: 4px;
}
.invoice-preview .qr-code {
    float: right;
    margin-left: 20px;
    max-width: 100px;
    max-height: 100px;
    border-radius: 8px;
    border: 1px solid #e1e8ed;
}
@media (max-width: 1024px) {
    .container {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    .preview-panel {
        position: static;
    }
    .payment-compact .payment-row {
        grid-template-columns: 1fr;
    }
}
.action-buttons {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}
.action-buttons .btn {
    flex: 1;
} 