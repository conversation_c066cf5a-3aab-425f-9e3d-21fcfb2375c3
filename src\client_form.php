<?php
// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['client_name'])) {
    $_SESSION['client'] = [
        'name' => $_POST['client_name'],
        'address' => $_POST['client_address'],
        'gstin_pan' => $_POST['client_gstin_pan'],
        'contact_number' => $_POST['client_contact_number'],
        'email' => $_POST['client_email'],
    ];
}

$client = $_SESSION['client'] ?? [
    'name' => '',
    'address' => '',
    'gstin_pan' => '',
    'contact_number' => '',
    'email' => '',
];
?>
<form method="post" class="row g-3 mb-4">
    <h5>Client / Recipient Details</h5>
    <div class="col-md-4">
        <label class="form-label">Client Name <span class="text-danger">*</span></label>
        <input type="text" name="client_name" class="form-control" value="<?= htmlspecialchars($client['name']) ?>" required>
    </div>
    <div class="col-md-8">
        <label class="form-label">Address</label>
        <textarea name="client_address" class="form-control" rows="2"><?= htmlspecialchars($client['address']) ?></textarea>
    </div>
    <div class="col-md-3">
        <label class="form-label">GSTIN / PAN</label>
        <input type="text" name="client_gstin_pan" class="form-control" value="<?= htmlspecialchars($client['gstin_pan']) ?>">
    </div>
    <div class="col-md-3">
        <label class="form-label">Contact Number</label>
        <input type="text" name="client_contact_number" class="form-control" value="<?= htmlspecialchars($client['contact_number']) ?>">
    </div>
    <div class="col-md-4">
        <label class="form-label">Email ID</label>
        <input type="email" name="client_email" class="form-control" value="<?= htmlspecialchars($client['email']) ?>">
    </div>
    <div class="col-12">
        <button type="submit" class="btn btn-primary">Save & Continue</button>
    </div>
</form> 