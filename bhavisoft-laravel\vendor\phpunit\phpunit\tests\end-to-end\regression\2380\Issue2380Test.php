<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\TestFixture;

use Generator;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class Issue2380Test extends TestCase
{
    public static function generatorData(): Generator
    {
        yield ['testing'];
    }

    #[DataProvider('generatorData')]
    public function testGeneratorProvider($data): void
    {
        $this->assertNotEmpty($data);
    }
}
